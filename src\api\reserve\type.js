import request from '@/utils/request'

// 查询会议室类型列表
export function listType(query) {
  return request({
    url: '/reserve/type/list',
    method: 'get',
    params: query
  })
}

// 查询会议室类型详细
export function getType(id) {
  return request({
    url: '/reserve/type/' + id,
    method: 'get'
  })
}

// 新增会议室类型
export function addType(data) {
  return request({
    url: '/reserve/type',
    method: 'post',
    data: data
  })
}

// 修改会议室类型
export function updateType(data) {
  return request({
    url: '/reserve/type',
    method: 'put',
    data: data
  })
}

// 删除会议室类型
export function delType(id) {
  return request({
    url: '/reserve/type/' + id,
    method: 'delete'
  })
}
