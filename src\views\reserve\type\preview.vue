<template>
  <div class="app-container">
    <!-- 会议室模块主内容区 -->
    <div class="meeting-room-container">
      <!-- 搜索区域 -->
      <div class="search-section">
        <el-card class="search-card" shadow="never">
          <el-form :model="searchParams" ref="searchRef" :inline="true" label-width="80px">
            <el-form-item label="会议室名称" prop="name">
              <el-input
                v-model="searchParams.name"
                placeholder="请输入会议室名称"
                clearable
                style="width: 200px"
                @keyup.enter="handleSearch"
              />
            </el-form-item>
            <el-form-item label="会议室类型" prop="type">
              <el-select
                v-model="searchParams.type"
                placeholder="请选择会议室类型"
                clearable
                style="width: 200px"
              >
                <el-option
                  v-for="item in roomTypes"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="容纳人数" prop="capacity">
              <el-select
                v-model="searchParams.capacity"
                placeholder="请选择容纳人数"
                clearable
                style="width: 200px"
              >
                <el-option label="1-10人" value="1-10" />
                <el-option label="11-20人" value="11-20" />
                <el-option label="21-50人" value="21-50" />
                <el-option label="50人以上" value="50+" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
              <el-button icon="Refresh" @click="resetSearch">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>

      <!-- 会议室列表区域 -->
      <div class="room-list-section">
        <el-row :gutter="20" v-loading="loading">
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6" v-for="room in roomList" :key="room.id">
            <el-card class="room-card" shadow="hover" :body-style="{ padding: '0px' }">
              <!-- 会议室图片 -->
              <div class="room-image-container">
                <el-image
                  :src="room.image || defaultRoomImage"
                  fit="cover"
                  class="room-image"
                  :preview-src-list="[room.image || defaultRoomImage]"
                  preview-teleported
                >
                  <template #error>
                    <div class="image-slot">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
                <!-- 状态标签 -->
                <div class="status-badge">
                  <el-tag
                    :type="room.status === '可用' ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ room.status }}
                  </el-tag>
                </div>
              </div>

              <!-- 会议室信息 -->
              <div class="room-info">
                <div class="room-header">
                  <h3 class="room-name">{{ room.name }}</h3>
                  <div class="room-type">{{ room.typeName }}</div>
                </div>

                <div class="room-details">
                  <div class="detail-item">
                    <el-icon><User /></el-icon>
                    <span>容纳人数：{{ room.capacity }}人</span>
                  </div>
                  <div class="detail-item">
                    <el-icon><Location /></el-icon>
                    <span>位置：{{ room.location || '暂无' }}</span>
                  </div>
                  <div class="detail-item" v-if="room.facilities">
                    <el-icon><Setting /></el-icon>
                    <span>设施：{{ room.facilities }}</span>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="room-actions">
                  <el-button
                    type="primary"
                    size="small"
                    :disabled="room.status !== '可用'"
                    @click="handleReserve(room)"
                    icon="Calendar"
                  >
                    立即预约
                  </el-button>
                  <el-button
                    type="info"
                    size="small"
                    plain
                    @click="handleViewDetails(room)"
                    icon="View"
                  >
                    查看详情
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 空状态 -->
        <el-empty v-if="!loading && roomList.length === 0" description="暂无会议室数据" />
      </div>

      <!-- 分页器 -->
      <div class="pagination-section" v-if="total > 0">
        <pagination
          :total="total"
          v-model:page="searchParams.pageNum"
          v-model:limit="searchParams.pageSize"
          @pagination="getRoomList"
        />
      </div>
    </div>

    <!-- 会议室详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="会议室详情"
      width="600px"
      append-to-body
    >
      <div class="room-detail-content" v-if="selectedRoom">
        <div class="detail-image">
          <el-image
            :src="selectedRoom.image || defaultRoomImage"
            fit="cover"
            style="width: 100%; height: 200px; border-radius: 8px;"
          />
        </div>
        <div class="detail-info">
          <h2>{{ selectedRoom.name }}</h2>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="会议室类型">{{ selectedRoom.typeName }}</el-descriptions-item>
            <el-descriptions-item label="容纳人数">{{ selectedRoom.capacity }}人</el-descriptions-item>
            <el-descriptions-item label="位置">{{ selectedRoom.location || '暂无' }}</el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="selectedRoom.status === '可用' ? 'success' : 'danger'">
                {{ selectedRoom.status }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="设施" :span="2">
              {{ selectedRoom.facilities || '暂无' }}
            </el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">
              {{ selectedRoom.remark || '暂无' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button
            type="primary"
            :disabled="selectedRoom?.status !== '可用'"
            @click="handleReserveFromDetail"
          >
            立即预约
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 预约模块对话框 -->
    <el-dialog
      v-model="reserveDialogVisible"
      title="会议室预约"
      width="700px"
      append-to-body
    >
      <el-form
        ref="reserveFormRef"
        :model="reserveForm"
        :rules="reserveRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="会议室" prop="roomId">
              <el-input v-model="reserveForm.roomName" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预约人" prop="reserveUser">
              <el-input v-model="reserveForm.reserveUser" placeholder="请输入预约人姓名" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="预约日期" prop="reserveDate">
              <el-date-picker
                v-model="reserveForm.reserveDate"
                type="date"
                placeholder="选择预约日期"
                style="width: 100%"
                :disabled-date="disabledDate"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="reserveForm.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-time-picker
                v-model="reserveForm.startTime"
                placeholder="选择开始时间"
                style="width: 100%"
                format="HH:mm"
                value-format="HH:mm"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-time-picker
                v-model="reserveForm.endTime"
                placeholder="选择结束时间"
                style="width: 100%"
                format="HH:mm"
                value-format="HH:mm"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="会议主题" prop="meetingTitle">
          <el-input v-model="reserveForm.meetingTitle" placeholder="请输入会议主题" />
        </el-form-item>

        <el-form-item label="参会人数" prop="attendeeCount">
          <el-input-number
            v-model="reserveForm.attendeeCount"
            :min="1"
            :max="selectedRoom?.capacity || 100"
            placeholder="请输入参会人数"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="会议描述" prop="description">
          <el-input
            v-model="reserveForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入会议描述（可选）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="reserveDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitReservation" :loading="reserveLoading">
            确认预约
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="RoomPreview">
import { listType } from "@/api/reserve/type"
import { Picture, User, Location, Setting, Calendar, View } from '@element-plus/icons-vue'

const { proxy } = getCurrentInstance()

// 响应式数据
const loading = ref(true)
const roomList = ref([])
const total = ref(0)
const detailDialogVisible = ref(false)
const reserveDialogVisible = ref(false)
const reserveLoading = ref(false)
const selectedRoom = ref(null)

// 默认会议室图片
const defaultRoomImage = ref('https://via.placeholder.com/400x200/409EFF/FFFFFF?text=Meeting+Room')

// 搜索参数
const searchParams = reactive({
  pageNum: 1,
  pageSize: 12,
  name: '',
  type: '',
  capacity: ''
})

// 会议室类型选项
const roomTypes = ref([
  { label: '小型会议室', value: 'small' },
  { label: '中型会议室', value: 'medium' },
  { label: '大型会议室', value: 'large' },
  { label: '多功能厅', value: 'multi' }
])

// 预约表单
const reserveForm = reactive({
  roomId: '',
  roomName: '',
  reserveUser: '',
  phone: '',
  reserveDate: '',
  startTime: '',
  endTime: '',
  meetingTitle: '',
  attendeeCount: 1,
  description: ''
})

// 预约表单验证规则
const reserveRules = {
  reserveUser: [
    { required: true, message: '请输入预约人姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  reserveDate: [
    { required: true, message: '请选择预约日期', trigger: 'change' }
  ],
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ],
  meetingTitle: [
    { required: true, message: '请输入会议主题', trigger: 'blur' }
  ],
  attendeeCount: [
    { required: true, message: '请输入参会人数', trigger: 'blur' }
  ]
}

/** 获取会议室列表 */
function getRoomList() {
  loading.value = true
  // 这里使用现有的会议室类型API，实际项目中应该有专门的会议室API
  listType(searchParams).then(response => {
    // 模拟会议室数据，基于会议室类型数据
    const mockRooms = response.rows.map((type, index) => ({
      id: type.id,
      name: `${type.name}${Math.floor(index / 2) + 1}号`,
      typeName: type.name,
      capacity: type.meetingCapacity || 10,
      status: Math.random() > 0.3 ? '可用' : '不可用',
      image: type.image,
      location: `${Math.floor(Math.random() * 10) + 1}楼${Math.floor(Math.random() * 20) + 1}室`,
      facilities: '投影仪、白板、空调、WiFi',
      remark: type.remark || '现代化会议室，设施齐全'
    }))

    roomList.value = mockRooms
    total.value = response.total
    loading.value = false
  }).catch(() => {
    loading.value = false
  })
}

/** 搜索操作 */
function handleSearch() {
  searchParams.pageNum = 1
  getRoomList()
}

/** 重置搜索 */
function resetSearch() {
  Object.assign(searchParams, {
    pageNum: 1,
    pageSize: 12,
    name: '',
    type: '',
    capacity: ''
  })
  getRoomList()
}

/** 查看详情 */
function handleViewDetails(room) {
  selectedRoom.value = room
  detailDialogVisible.value = true
}

/** 预约会议室 */
function handleReserve(room) {
  selectedRoom.value = room
  resetReserveForm()
  reserveForm.roomId = room.id
  reserveForm.roomName = room.name
  reserveDialogVisible.value = true
}

/** 从详情页预约 */
function handleReserveFromDetail() {
  detailDialogVisible.value = false
  handleReserve(selectedRoom.value)
}

/** 重置预约表单 */
function resetReserveForm() {
  Object.assign(reserveForm, {
    roomId: '',
    roomName: '',
    reserveUser: '',
    phone: '',
    reserveDate: '',
    startTime: '',
    endTime: '',
    meetingTitle: '',
    attendeeCount: 1,
    description: ''
  })
}

/** 禁用过去的日期 */
function disabledDate(time) {
  return time.getTime() < Date.now() - 8.64e7
}

/** 提交预约 */
function submitReservation() {
  proxy.$refs.reserveFormRef.validate(valid => {
    if (valid) {
      // 验证时间逻辑
      if (reserveForm.startTime >= reserveForm.endTime) {
        proxy.$modal.msgError('结束时间必须大于开始时间')
        return
      }

      // 验证参会人数
      if (reserveForm.attendeeCount > selectedRoom.value.capacity) {
        proxy.$modal.msgError(`参会人数不能超过会议室容量(${selectedRoom.value.capacity}人)`)
        return
      }

      reserveLoading.value = true

      // 模拟API调用
      setTimeout(() => {
        proxy.$modal.msgSuccess('预约成功！')
        reserveDialogVisible.value = false
        reserveLoading.value = false
        resetReserveForm()
        // 这里可以刷新会议室状态
        getRoomList()
      }, 1000)
    }
  })
}

// 初始化数据
onMounted(() => {
  getRoomList()
})
</script>

<style lang="scss" scoped>
.meeting-room-container {
  .search-section {
    margin-bottom: 20px;

    .search-card {
      border-radius: 8px;

      :deep(.el-card__body) {
        padding: 20px;
      }
    }
  }

  .room-list-section {
    margin-bottom: 20px;

    .room-card {
      margin-bottom: 20px;
      border-radius: 12px;
      overflow: hidden;
      transition: all 0.3s ease;
      height: 100%;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      .room-image-container {
        position: relative;
        height: 200px;
        overflow: hidden;

        .room-image {
          width: 100%;
          height: 100%;
          transition: transform 0.3s ease;

          &:hover {
            transform: scale(1.05);
          }
        }

        .image-slot {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 100%;
          background-color: var(--el-fill-color-light);
          color: var(--el-text-color-placeholder);
          font-size: 30px;
        }

        .status-badge {
          position: absolute;
          top: 10px;
          right: 10px;
          z-index: 1;
        }
      }

      .room-info {
        padding: 20px;

        .room-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;

          .room-name {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: var(--el-text-color-primary);
          }

          .room-type {
            padding: 4px 8px;
            background-color: var(--el-color-primary-light-9);
            color: var(--el-color-primary);
            border-radius: 4px;
            font-size: 12px;
          }
        }

        .room-details {
          margin-bottom: 20px;

          .detail-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            color: var(--el-text-color-regular);
            font-size: 14px;

            .el-icon {
              margin-right: 8px;
              color: var(--el-color-primary);
            }
          }
        }

        .room-actions {
          display: flex;
          gap: 10px;

          .el-button {
            flex: 1;
          }
        }
      }
    }
  }

  .pagination-section {
    display: flex;
    justify-content: center;
    margin-top: 30px;
  }
}

// 详情对话框样式
.room-detail-content {
  .detail-image {
    margin-bottom: 20px;
  }

  .detail-info {
    h2 {
      margin: 0 0 20px 0;
      color: var(--el-text-color-primary);
      font-size: 24px;
      font-weight: 600;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .meeting-room-container {
    .search-section {
      .search-card {
        :deep(.el-card__body) {
          padding: 15px;
        }

        :deep(.el-form) {
          .el-form-item {
            margin-bottom: 15px;

            .el-input,
            .el-select {
              width: 100% !important;
            }
          }
        }
      }
    }

    .room-list-section {
      .room-card {
        .room-info {
          padding: 15px;

          .room-actions {
            flex-direction: column;

            .el-button {
              margin-bottom: 8px;
            }
          }
        }
      }
    }
  }
}

// 暗色主题适配
:deep(.dark) {
  .room-card {
    background-color: var(--el-bg-color-page);
    border-color: var(--el-border-color);
  }

  .search-card {
    background-color: var(--el-bg-color-page);
    border-color: var(--el-border-color);
  }
}
</style>