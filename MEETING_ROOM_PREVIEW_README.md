# 会议室预览模块功能说明

## 概述
在 `src/views/reserve/type/preview.vue` 文件中添加了完整的会议室预览和预约功能，包括搜索、卡片展示、详情查看和预约模块。

## 新增功能详细说明

### 1. 会议室模块主内容区

#### 搜索框功能
- **会议室名称搜索**：支持模糊搜索会议室名称
- **会议室类型筛选**：下拉选择不同类型的会议室（小型、中型、大型、多功能厅）
- **容纳人数筛选**：按人数范围筛选（1-10人、11-20人、21-50人、50人以上）
- **搜索和重置按钮**：快速搜索和清空筛选条件
- **响应式设计**：在移动端自动适配为垂直布局

#### 会议室列表卡片展示
每个会议室卡片包含以下信息：
- **会议室图片**：支持图片预览，鼠标悬停放大效果
- **会议室名称**：清晰显示会议室名称
- **会议室类型**：彩色标签显示类型
- **容纳人数**：显示最大容纳人数
- **当前状态**：可用/不可用状态标签（绿色/红色）
- **位置信息**：显示会议室具体位置
- **设施信息**：列出可用设施（投影仪、白板等）
- **操作按钮**：
  - **立即预约**：直接跳转到预约表单
  - **查看详情**：打开详情对话框

#### 卡片交互效果
- **悬停效果**：鼠标悬停时卡片上浮并增加阴影
- **图片缩放**：图片支持预览和悬停缩放
- **状态标识**：不可用的会议室预约按钮自动禁用
- **响应式布局**：
  - 超大屏幕：6列布局
  - 大屏幕：4列布局
  - 中等屏幕：3列布局
  - 小屏幕：2列布局
  - 移动端：1列布局

### 2. 会议室模块分页器
- **智能分页**：当会议室数量超过每页显示数量时自动显示
- **页面大小**：默认每页显示12个会议室
- **页码跳转**：支持直接跳转到指定页面
- **总数显示**：显示会议室总数量

### 3. 预约模块功能

#### 会议室详情对话框
- **大图展示**：会议室高清图片展示
- **详细信息**：使用描述列表组件展示完整信息
- **快速预约**：从详情页直接跳转到预约表单

#### 预约表单功能
包含以下字段：
- **会议室信息**：自动填充选中的会议室名称（只读）
- **预约人姓名**：必填，预约人的真实姓名
- **联系电话**：必填，支持手机号格式验证
- **预约日期**：日期选择器，禁用过去的日期
- **时间段**：开始时间和结束时间选择
- **会议主题**：必填，会议的主要议题
- **参会人数**：数字输入，不能超过会议室容量
- **会议描述**：可选，会议的详细描述

#### 表单验证功能
- **必填字段验证**：确保关键信息完整
- **手机号格式验证**：验证联系电话格式正确性
- **时间逻辑验证**：确保结束时间大于开始时间
- **容量限制验证**：参会人数不能超过会议室最大容量
- **日期限制**：不能预约过去的日期

#### 预约提交处理
- **加载状态**：提交时显示加载动画
- **成功反馈**：预约成功后显示成功消息
- **状态更新**：预约后刷新会议室状态
- **表单重置**：成功后自动清空表单

## 技术实现特点

### 1. 主题适配
- **颜色方案**：完全适配若依框架的主题色彩
- **暗色模式**：支持暗色主题切换
- **Element Plus**：使用框架统一的组件库

### 2. 响应式设计
- **移动端优化**：在小屏幕设备上自动调整布局
- **触摸友好**：按钮大小和间距适合触摸操作
- **弹性布局**：使用CSS Grid和Flexbox实现自适应

### 3. 用户体验
- **加载状态**：数据加载时显示骨架屏
- **空状态处理**：无数据时显示友好的空状态页面
- **错误处理**：网络错误时的友好提示
- **操作反馈**：所有操作都有明确的成功/失败反馈

### 4. 性能优化
- **图片懒加载**：大图片支持懒加载
- **分页加载**：避免一次性加载过多数据
- **组件复用**：使用Vue3的组合式API提高性能

## 文件结构

```
src/views/reserve/type/preview.vue
├── template (模板部分)
│   ├── 搜索区域
│   ├── 会议室列表区域
│   ├── 分页器
│   ├── 详情对话框
│   └── 预约对话框
├── script (逻辑部分)
│   ├── 数据定义
│   ├── 搜索功能
│   ├── 列表管理
│   ├── 详情查看
│   ├── 预约处理
│   └── 表单验证
└── style (样式部分)
    ├── 卡片样式
    ├── 响应式布局
    ├── 交互效果
    └── 主题适配
```

## 路由配置
在 `src/router/index.js` 中添加了新的路由：
- 路径：`/reserve/room-preview/index`
- 权限：`reserve:type:list`
- 菜单激活：`/reserve/type`

## 访问方式
1. 在会议室类型管理页面点击"会议室预览"按钮
2. 直接访问路由：`/reserve/room-preview/index`

## 数据模拟
由于当前使用的是会议室类型API，代码中包含了数据转换逻辑，将类型数据模拟为具体的会议室数据。在实际项目中，应该：
1. 创建专门的会议室API接口
2. 创建会议室预约API接口
3. 替换模拟数据为真实API调用

## 扩展建议
1. **实时状态更新**：集成WebSocket实现会议室状态实时更新
2. **日历视图**：添加日历组件显示会议室占用情况
3. **批量操作**：支持批量预约多个会议室
4. **权限控制**：根据用户角色限制预约权限
5. **通知系统**：预约成功后发送邮件或短信通知
